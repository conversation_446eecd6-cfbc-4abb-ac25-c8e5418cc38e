package com.ggec.hs01;

import android.app.Application;
import android.util.Log;

import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.yobtsdkserver.YoBTInit;
import com.ggec.yotasdk.YOTAApi;

import java.io.File;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description:     
 * 应用程序类
 * 用于集中管理所有模块的初始化逻辑
 */
public class GGECHSApplication extends Application {
    private static final String TAG = "GGECHSApplication";
    
    // 功能模块启用状态控制
    private boolean mLogEnabled = true;       // 是否启用日志
    private boolean mBluetoothEnabled = true; // 是否启用蓝牙功能
    private boolean mOtaEnabled = true;       // 是否启用OTA功能
    
    // SDK初始化器
    private YoBTInit mBtInit;
    private YOTAApi mOtaApi;
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        // 按照依赖关系顺序初始化各模块
        initLogger();
        
        if (mBluetoothEnabled) {
            initBtSdk();
        }
        
        if (mOtaEnabled) {
            initOtaService();
        }
        
        // 创建应用私有目录
        createPrivateDirectory();
        
        Log.i(TAG, "应用初始化完成");
    }
    
    /**
     * 创建应用私有目录
     * 确保/files目录存在
     */
    private void createPrivateDirectory() {
        File filesDir = getExternalFilesDir(null);
        if (filesDir == null) {
            Log.e(TAG, "无法获取外部存储的私有目录。");
            return;
        }

        if (!filesDir.exists()) {
            Log.d(TAG, "私有目录不存在，正在创建: " + filesDir.getAbsolutePath());
            if (filesDir.mkdirs()) {
                Log.i(TAG, "私有目录创建成功。");
            } else {
                Log.e(TAG, "私有目录创建失败。");
            }
        } else {
            Log.i(TAG, "私有目录已存在: " + filesDir.getAbsolutePath());
        }
    }
    
    /**
     * 初始化日志系统
     */
    private void initLogger() {
        // 使用YoBLEApi设置日志状态
        YoBLEApi.getInstance().setLogEnabled(mLogEnabled);
        Log.i(TAG, "日志系统初始化完成，日志状态：" + (mLogEnabled ? "已启用" : "已禁用"));
    }
    
    /**
     * 初始化蓝牙服务
     */
    private void initBtSdk() {
        Log.i(TAG, "初始化蓝牙SDK");
        
        // 使用YoBTInit来初始化SDK
        mBtInit = YoBTInit.getInstance();
        
        // 设置调试模式（与日志设置保持一致）并初始化
        mBtInit.initWithDebug(this, mLogEnabled);
        
        Log.i(TAG, "蓝牙SDK初始化完成");
    }
    
    /**
     * 释放蓝牙服务资源
     */
    private void releaseBtSdk() {
        Log.i(TAG, "释放蓝牙SDK资源");
        
        // 使用YoBTInit统一释放资源
        if (mBtInit != null && mBtInit.isInitialized()) {
            mBtInit.release();
            mBtInit = null;
        }
    }
    
    /**
     * 初始化OTA服务
     */
    private void initOtaService() {
        Log.i(TAG, "初始化OTA服务");
        
        // 使用YOTAApi接口获取实例（单例模式）
        mOtaApi = YOTAApi.getInstance(getApplicationContext());
        
        Log.i(TAG, "OTA服务初始化完成");
    }
    
    /**
     * 设置是否启用OTA功能
     * @param enabled 是否启用
     */
    public void setOtaEnabled(boolean enabled) {
        this.mOtaEnabled = enabled;
        
        // 如果禁用OTA功能，则释放资源
        if (!enabled) {
            mOtaApi = null;
        } 
        // 如果启用OTA功能且尚未初始化，则初始化
        else if (enabled && mOtaApi == null) {
            initOtaService();
        }
    }
    
    /**
     * 获取YOTAApi实例
     * 使用单例模式确保全局只有一个实例
     * 
     * @return YOTAApi实例
     */
    public YOTAApi getOtaApi() {
        if (mOtaApi == null && mOtaEnabled) {
            initOtaService();
        }
        return mOtaApi;
    }
    
    /**
     * 设置是否启用日志
     * @param enabled 是否启用
     */
    public void setLogEnabled(boolean enabled) {
        this.mLogEnabled = enabled;
        // 通过YoBLEApi设置日志状态
        YoBLEApi.getInstance().setLogEnabled(enabled);
        
        // 同步更新SDK的调试模式
        if (mBtInit != null && mBtInit.isInitialized()) {
            mBtInit.setDebugMode(enabled);
        }
    }
    
    /**
     * 设置是否启用蓝牙功能
     * @param enabled 是否启用
     */
    public void setBluetoothEnabled(boolean enabled) {
        this.mBluetoothEnabled = enabled;
        
        // 如果禁用蓝牙功能，则释放资源
        if (!enabled && mBtInit != null && mBtInit.isInitialized()) {
            releaseBtSdk();
        } 
        // 如果启用蓝牙功能且尚未初始化，则初始化
        else if (enabled && (mBtInit == null || !mBtInit.isInitialized())) {
            initBtSdk();
        }
    }
    
    /**
     * 获取YoBLEApi实例
     * 通过YoBLEInitializer获取，确保正确初始化
     */
    public YoBLEApi getBleApi() {
        if (mBtInit != null && mBtInit.isInitialized()) {
            return mBtInit.getBleApi();
        }
        return null;
    }
    
    /**
     * 获取YoCommandApi实例
     * 通过YoBLEInitializer获取，确保正确初始化
     */
    public YoCommandApi getCommandApi() {
        if (mBtInit != null && mBtInit.isInitialized()) {
            return mBtInit.getCommandApi();
        }
        return null;
    }
    
    @Override
    public void onTerminate() {
        super.onTerminate();
        Log.i(TAG, "应用终止，开始释放资源");

        // 释放蓝牙SDK资源
        if (mBluetoothEnabled) {
            releaseBtSdk();
        }
        
        // 释放其他可能需要清理的资源
        mOtaApi = null;
        
        Log.i(TAG, "所有资源已释放");
    }
}
