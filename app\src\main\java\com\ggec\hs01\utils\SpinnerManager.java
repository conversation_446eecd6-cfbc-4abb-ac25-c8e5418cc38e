package com.ggec.hs01.utils;

import android.content.Context;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Spinner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * Spinner管理器
 * 用于管理多个Spinner的状态，实现功能互斥
 */
public class SpinnerManager {
    private final List<Spinner> spinners = new ArrayList<>();
    private final Map<Spinner, Boolean[]> disabledItemsMap = new HashMap<>();
    private final ArrayAdapter<String> adapter;
    private final String[] options;
    private static final int NONE_OPTION_INDEX = 8; // "无"选项的索引
    
    // 当前活动的Spinner
    private Spinner currentActiveSpinner = null;

    /**
     * 构造方法
     * @param context 上下文
     * @param options 选项数组
     * @param spinnerLayoutResId spinner布局资源ID
     * @param dropdownLayoutResId 下拉列表布局资源ID
     */
    public SpinnerManager(Context context, String[] options, int spinnerLayoutResId, int dropdownLayoutResId) {
        this.options = options;
        
        // 创建自定义适配器
        adapter = new ArrayAdapter<String>(context, spinnerLayoutResId, options) {
            @Override
            public boolean isEnabled(int position) {
                // 获取当前正在显示下拉列表的spinner
                Spinner activeSpinner = currentActiveSpinner;
                
                if (activeSpinner != null && disabledItemsMap.containsKey(activeSpinner)) {
                    Boolean[] disabledItems = disabledItemsMap.get(activeSpinner);
                    // 如果该项被禁用，则返回false
                    if (position < disabledItems.length && disabledItems[position]) {
                        return false;
                    }
                }
                
                return true;
            }
            
            @Override
            public View getDropDownView(int position, View convertView, android.view.ViewGroup parent) {
                View view = super.getDropDownView(position, convertView, parent);
                
                // 获取当前正在显示下拉列表的spinner
                Spinner activeSpinner = currentActiveSpinner;
                
                // 设置禁用项的透明度
                if (activeSpinner != null && disabledItemsMap.containsKey(activeSpinner)) {
                    Boolean[] disabledItems = disabledItemsMap.get(activeSpinner);
                    if (position < disabledItems.length && disabledItems[position]) {
                        view.setAlpha(0.3f);
                    } else {
                        view.setAlpha(1.0f);
                    }
                }
                
                return view;
            }
        };
        
        adapter.setDropDownViewResource(dropdownLayoutResId);
    }
    
    /**
     * 添加Spinner
     * @param spinner 要添加的Spinner
     */
    public void addSpinner(Spinner spinner) {
        spinners.add(spinner);
        disabledItemsMap.put(spinner, new Boolean[options.length]);
        spinner.setAdapter(adapter);
        
        // 为Spinner添加点击监听器，记录当前活动的Spinner
        spinner.setOnTouchListener((v, event) -> {
            currentActiveSpinner = spinner;
            return false; // 返回false以不拦截事件
        });
    }
    
    /**
     * 更新Spinner状态
     * 根据当前所有spinner的选中项，更新每个spinner的禁用项
     */
    public void updateSpinnerStates() {
        // 重置所有spinner的禁用项
        for (Spinner spinner : spinners) {
            Boolean[] disabledItems = disabledItemsMap.get(spinner);
            if (disabledItems != null) {
                for (int i = 0; i < disabledItems.length; i++) {
                    disabledItems[i] = false;
                }
            }
        }
        
        // 记录已选择的项
        List<Integer> selectedItems = new ArrayList<>();
        for (Spinner spinner : spinners) {
            int selected = spinner.getSelectedItemPosition();
            // 不要将"无"选项添加到已选择项中
            if (selected != NONE_OPTION_INDEX) {
                selectedItems.add(selected);
            }
        }
        
        // 为每个spinner更新禁用项
        for (Spinner spinner : spinners) {
            Boolean[] disabledItems = disabledItemsMap.get(spinner);
            if (disabledItems != null) {
                int currentSelected = spinner.getSelectedItemPosition();
                
                for (int selected : selectedItems) {
                    // 如果该项已被其他spinner选中，则禁用它，但不禁用当前spinner自己的选中项
                    if (selected != currentSelected) {
                        disabledItems[selected] = true;
                    }
                }
            }
        }
        
        // 通知适配器数据已更改
        adapter.notifyDataSetChanged();
    }
    
    /**
     * 获取适配器
     * @return 适配器
     */
    public ArrayAdapter<String> getAdapter() {
        return adapter;
    }
} 