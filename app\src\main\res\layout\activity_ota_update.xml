<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ota_update_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.OtaUpdateActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/comp_background_gray"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="固件升级"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 使用ScrollView确保所有内容可滚动 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- OTA状态检测卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="OTA状态"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"
                        android:layout_marginBottom="12dp"/>

                    <!-- OTA状态检测栏 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="OTA条件检测"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <TextView
                            android:id="@+id/tv_ota_status"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="未检测"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"/>
                    </LinearLayout>

                    <!-- OTA进度显示 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="OTA升级进度"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <TextView
                            android:id="@+id/tv_ota_progress"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0%"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"/>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- OTA状态检测按钮 -->
            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_check_ota_status"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="检测OTA条件"
                android:textSize="16sp"
                android:layout_marginBottom="16dp"
                android:enabled="false" />

            <!-- OTA升级按钮 -->
            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_start_ota"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="开始OTA升级"
                android:textSize="16sp"
                android:enabled="false" />

            <!-- 查看固件文件按钮 -->
            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_check_firmware"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="16dp"
                android:text="查看固件文件"
                android:textSize="16sp" />

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 