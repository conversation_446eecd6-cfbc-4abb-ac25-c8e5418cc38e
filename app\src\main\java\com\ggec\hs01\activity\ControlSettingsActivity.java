package com.ggec.hs01.activity;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.Toast;

import android.util.Log;
import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.utils.SpinnerManager;
import com.ggec.hs01.view.HMButton;
import com.ggec.hs01.GGECHSApplication;


/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * 控制设置界面
 * 允许用户自定义耳机的按键控制行为
 */
public class ControlSettingsActivity extends AppCompatActivity {
    private static final String TAG = "ControlSettingsActivity";
    
    // Spinner组件
    private Spinner spinnerLeftTapTwice;
    private Spinner spinnerRightTapTwice;
    private Spinner spinnerLeftTapTriple;
    private Spinner spinnerRightTapTriple;
    
    // 恢复默认按钮
    private HMButton btnResetControls;
    
    // Spinner管理器
    private SpinnerManager spinnerManager;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;
    
    // YoCommandApi 实例
    private YoCommandApi commandApi;
    
    // SharedPreferences键名常量
    private static final String PREFS_NAME = "EarbudsControlPrefs";
    private static final String KEY_LEFT_TAP_TWICE = "left_tap_twice";
    private static final String KEY_RIGHT_TAP_TWICE = "right_tap_twice";
    private static final String KEY_LEFT_TAP_TRIPLE = "left_tap_triple";
    private static final String KEY_RIGHT_TAP_TRIPLE = "right_tap_triple";
    
    // 默认选中项的索引
    private int defaultLeftTapTwiceIndex = 0; // 播放+暂停
    private int defaultRightTapTwiceIndex = 5; // 语音助手
    private int defaultLeftTapTripleIndex = 3; // 上一曲
    private int defaultRightTapTripleIndex = 4; // 下一曲

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_control_settings);
        
        // 获取YoBLEApi实例
        bleApi = ((GGECHSApplication) getApplication()).getBleApi();
        
        // 获取YoCommandApi实例
        commandApi = ((GGECHSApplication) getApplication()).getCommandApi();
        
        // 初始化返回按钮
        initBackButton();
        
        // 初始化下拉菜单
        initSpinners();
        
        // 初始化重置按钮
        initResetButton();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.control_settings_main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
    }
    
    @Override
    protected void onStart() {
        super.onStart();
        
        // 确保已连接设备，如果没有则返回连接页面
        if (!bleApi.isDeviceConnected()) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
    }
    
    @Override
    protected void onStop() {
        super.onStop();
        // 不需要额外操作
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 不需要额外操作
    }
    
    /**
     * 初始化返回按钮
     */
    private void initBackButton() {
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
    }
    
    /**
     * 初始化下拉菜单
     */
    private void initSpinners() {
        // 获取控件引用
        spinnerLeftTapTwice = findViewById(R.id.spinner_left_tap_twice);
        spinnerRightTapTwice = findViewById(R.id.spinner_right_tap_twice);
        spinnerLeftTapTriple = findViewById(R.id.spinner_left_tap_triple);
        spinnerRightTapTriple = findViewById(R.id.spinner_right_tap_triple);
        
        // 获取字符串数组资源
        String[] controlOptions = getResources().getStringArray(R.array.earbuds_control_options);
        
        // 创建SpinnerManager并添加所有Spinner
        spinnerManager = new SpinnerManager(
                this, 
                controlOptions, 
                R.layout.item_spinner, 
                R.layout.item_spinner_dropdown);
        
        spinnerManager.addSpinner(spinnerLeftTapTwice);
        spinnerManager.addSpinner(spinnerRightTapTwice);
        spinnerManager.addSpinner(spinnerLeftTapTriple);
        spinnerManager.addSpinner(spinnerRightTapTriple);
        
        // 加载保存的设置
        loadSavedSettings();
        
        // 设置选择监听器
        spinnerLeftTapTwice.setOnItemSelectedListener(new ControlSelectionListener(
                KEY_LEFT_TAP_TWICE, YoCommandApi.EAR_LEFT, YoCommandApi.TAP_TWICE));
        
        spinnerRightTapTwice.setOnItemSelectedListener(new ControlSelectionListener(
                KEY_RIGHT_TAP_TWICE, YoCommandApi.EAR_RIGHT, YoCommandApi.TAP_TWICE));
        
        spinnerLeftTapTriple.setOnItemSelectedListener(new ControlSelectionListener(
                KEY_LEFT_TAP_TRIPLE, YoCommandApi.EAR_LEFT, YoCommandApi.TAP_TRIPLE));
        
        spinnerRightTapTriple.setOnItemSelectedListener(new ControlSelectionListener(
                KEY_RIGHT_TAP_TRIPLE, YoCommandApi.EAR_RIGHT, YoCommandApi.TAP_TRIPLE));
        
        // 初始化时更新一次Spinner状态
        spinnerManager.updateSpinnerStates();
    }
    
    /**
     * 初始化重置按钮
     */
    private void initResetButton() {
        btnResetControls = findViewById(R.id.btn_reset_controls);
        btnResetControls.setOnClickListener(v -> showResetConfirmDialog());
    }
    
    /**
     * 显示重置确认对话框
     */
    private void showResetConfirmDialog() {
        new AlertDialog.Builder(this)
                .setTitle("恢复默认设置")
                .setMessage("确定要恢复所有控制设置为默认值吗？")
                .setPositiveButton("确定", (dialog, which) -> {
                    resetToDefaults(true);
                    dialog.dismiss();
                })
                .setNegativeButton("取消", (dialog, which) -> {
                    resetToDefaults(false);
                    dialog.dismiss();
                })
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }
    
    /**
     * 加载保存的设置
     */
    private void loadSavedSettings() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        
        // 获取保存的索引值，如果没有则使用默认值
        int leftTapTwice = prefs.getInt(KEY_LEFT_TAP_TWICE, defaultLeftTapTwiceIndex);
        int rightTapTwice = prefs.getInt(KEY_RIGHT_TAP_TWICE, defaultRightTapTwiceIndex);
        int leftTapTriple = prefs.getInt(KEY_LEFT_TAP_TRIPLE, defaultLeftTapTripleIndex);
        int rightTapTriple = prefs.getInt(KEY_RIGHT_TAP_TRIPLE, defaultRightTapTripleIndex);
        
        // 设置选中项
        spinnerLeftTapTwice.setSelection(leftTapTwice);
        spinnerRightTapTwice.setSelection(rightTapTwice);
        spinnerLeftTapTriple.setSelection(leftTapTriple);
        spinnerRightTapTriple.setSelection(rightTapTriple);
    }
    
    /**
     * 保存设置
     */
    private void saveSettings(String key, int position) {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(key, position);
        editor.apply();
    }
    
    /**
     * 重置为默认设置
     * @param isConfirm 是否确认重置
     */
    private void resetToDefaults(boolean isConfirm) {
        if (isConfirm) {
            // 重置为默认选项
            spinnerLeftTapTwice.setSelection(defaultLeftTapTwiceIndex);
            spinnerRightTapTwice.setSelection(defaultRightTapTwiceIndex);
            spinnerLeftTapTriple.setSelection(defaultLeftTapTripleIndex);
            spinnerRightTapTriple.setSelection(defaultRightTapTripleIndex);
            
            // 保存默认设置
            saveSettings(KEY_LEFT_TAP_TWICE, defaultLeftTapTwiceIndex);
            saveSettings(KEY_RIGHT_TAP_TWICE, defaultRightTapTwiceIndex);
            saveSettings(KEY_LEFT_TAP_TRIPLE, defaultLeftTapTripleIndex);
            saveSettings(KEY_RIGHT_TAP_TRIPLE, defaultRightTapTripleIndex);
            
            // 使用YoCommandApi发送重置命令
            commandApi.resetControlSettings((code) -> {
                runOnUiThread(() -> {
                    // 显示操作结果
                    String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                            "设置成功" : "设置失败";
                    Toast.makeText(ControlSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                    
                    // 如果失败，可以在这里添加处理逻辑
                });
            });
        }
    }
    
    /**
     * 显示提示信息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    /**
     * 下拉菜单选择监听器
     */
    private class ControlSelectionListener implements AdapterView.OnItemSelectedListener {
        private final String preferenceKey;
        private final int earSide;
        private final int tapType;
        private boolean isInitialSelection = true;
        
        public ControlSelectionListener(String preferenceKey, int earSide, int tapType) {
            this.preferenceKey = preferenceKey;
            this.earSide = earSide;
            this.tapType = tapType;
        }
        
        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
            // 忽略初始选择（由loadSavedSettings触发）
            if (isInitialSelection) {
                isInitialSelection = false;
                return;
            }
            
            // 记录日志
            // position对应的功能列表：
            // 0 - 播放/暂停
            // 1 - 音量+
            // 2 - 音量-
            // 3 - 上一曲
            // 4 - 下一曲
            // 5 - 语音助手
            // 6 - EQ切换
            // 7 - 游
            Log.d(TAG, "控制功能选择: 耳机侧=" + earSide + ", 点击类型=" + tapType + ", 功能代码=" + position);
            
            // 保存设置到SharedPreferences
            saveSettings(preferenceKey, position);
            
            // 使用YoCommandApi发送命令
            commandApi.setControlFunction(earSide, tapType, position, (code) -> {
                runOnUiThread(() -> {
                    // 显示操作结果
                    String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                            "设置成功" : "设置失败";
                    Toast.makeText(ControlSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                    
                    // 如果失败，需要在UI线程中更新Spinner状态
                    if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                        // 重新加载保存的设置
                        loadSavedSettings();
                    }
                });
            });
        }
        
        @Override
        public void onNothingSelected(AdapterView<?> parent) {
            // 不需要处理
        }
    }
} 