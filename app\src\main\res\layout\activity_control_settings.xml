<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/control_settings_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.ControlSettingsActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/comp_background_gray"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="控制设置"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 使用ScrollView确保所有内容可滚动 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 控制设置标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="控制设置"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="8dp"/>
                
            <!-- 自定义按键控制说明 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="自定义按键控制"
                android:textSize="14sp"
                android:textColor="@color/font_secondary"
                android:layout_marginBottom="16dp"/>

            <!-- 自定义按键控制卡片 - 使用表格式布局 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <TableLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:stretchColumns="1">

                    <!-- 标题行 -->
                    <TableRow
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="触击方式"
                            android:textStyle="bold"
                            android:textColor="@color/brand"
                            android:padding="8dp"/>

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="功能选择"
                            android:textStyle="bold"
                            android:textColor="@color/brand"
                            android:gravity="center"
                            android:padding="8dp"/>
                    </TableRow>

                    <!-- 左耳触击2次 -->
                    <TableRow
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:background="?attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="左耳触击2次"
                                android:textColor="@color/font_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="默认: 播放+暂停"
                                android:textColor="@color/font_secondary"
                                android:textSize="12sp"
                                android:layout_marginTop="2dp"/>
                        </LinearLayout>

                        <Spinner
                            android:id="@+id/spinner_left_tap_twice"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:background="@drawable/bg_spinner"
                            android:popupBackground="@color/background_primary"
                            android:padding="8dp"/>
                    </TableRow>

                    <!-- 右耳触击2次 -->
                    <TableRow
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:background="?attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="右耳触击2次"
                                android:textColor="@color/font_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="默认: 语音助手"
                                android:textColor="@color/font_secondary"
                                android:textSize="12sp"
                                android:layout_marginTop="2dp"/>
                        </LinearLayout>

                        <Spinner
                            android:id="@+id/spinner_right_tap_twice"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:background="@drawable/bg_spinner"
                            android:popupBackground="@color/background_primary"
                            android:padding="8dp"/>
                    </TableRow>

                    <!-- 左耳触击3次 -->
                    <TableRow
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:background="?attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="左耳触击3次"
                                android:textColor="@color/font_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="默认: 上一曲"
                                android:textColor="@color/font_secondary"
                                android:textSize="12sp"
                                android:layout_marginTop="2dp"/>
                        </LinearLayout>

                        <Spinner
                            android:id="@+id/spinner_left_tap_triple"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:background="@drawable/bg_spinner"
                            android:popupBackground="@color/background_primary"
                            android:padding="8dp"/>
                    </TableRow>

                    <!-- 右耳触击3次 -->
                    <TableRow
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="右耳触击3次"
                                android:textColor="@color/font_primary"
                                android:textSize="14sp"
                                android:textStyle="bold"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="默认: 下一曲"
                                android:textColor="@color/font_secondary"
                                android:textSize="12sp"
                                android:layout_marginTop="2dp"/>
                        </LinearLayout>

                        <Spinner
                            android:id="@+id/spinner_right_tap_triple"
                            android:layout_width="0dp"
                            android:layout_height="48dp"
                            android:layout_weight="1"
                            android:background="@drawable/bg_spinner"
                            android:popupBackground="@color/background_primary"
                            android:padding="8dp"/>
                    </TableRow>
                </TableLayout>
            </androidx.cardview.widget.CardView>

            <!-- 恢复默认控制按钮 - 独立样式 -->
            <com.ggec.hs01.view.HMButton
                android:id="@+id/btn_reset_controls"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:text="恢复默认控制"
                android:textSize="16sp"/>

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 