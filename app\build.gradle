plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.ggec.hs01'
    compileSdk 35

    defaultConfig {
        applicationId "com.ggec.hs01"
        minSdk 24
        targetSdk 32
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    // 解决.so库冲突问题 - 使用通配符匹配所有可能冲突的库文件
    packagingOptions {
        // 使用通配符匹配所有.so文件，选择第一个遇到的
        pickFirst 'lib/**/*.so'
        
        // 明确列出已知冲突的文件
        pickFirst 'lib/arm64-v8a/libimagequant.so'
        pickFirst 'lib/armeabi-v7a/libimagequant.so'
        pickFirst 'lib/arm64-v8a/liblcms2.so'
        pickFirst 'lib/armeabi-v7a/liblcms2.so'
        pickFirst 'lib/arm64-v8a/libpng.so'
        pickFirst 'lib/armeabi-v7a/libpng.so'
        pickFirst 'lib/arm64-v8a/libjpeg.so'
        pickFirst 'lib/armeabi-v7a/libjpeg.so'
        
        // 排除其他可能冲突的资源
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
    }
    
    lint {
        baseline = file("lint-baseline.xml")
        abortOnError = false
    }
}

dependencies {

    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}