package com.ggec.hs01.utils;

import android.bluetooth.BluetoothDevice;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.hs01.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * 蓝牙设备适配器，用于在RecyclerView中显示扫描到的蓝牙设备
 */
public class BluetoothDeviceAdapter extends RecyclerView.Adapter<BluetoothDeviceAdapter.DeviceViewHolder> {

    private final List<BluetoothDevice> deviceList = new ArrayList<>();
    private final List<Integer> rssiList = new ArrayList<>();
    private OnDeviceClickListener listener;

    public interface OnDeviceClickListener {
        void onDeviceClick(BluetoothDevice device);
    }

    public BluetoothDeviceAdapter() {
    }

    public void setOnDeviceClickListener(OnDeviceClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public DeviceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_ble_device, parent, false);
        return new DeviceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        BluetoothDevice device = deviceList.get(position);
        int rssi = rssiList.get(position);
        
        // 设置设备名称，如果为空则显示MAC地址
        String deviceName = device.getName();
        if (deviceName == null || deviceName.isEmpty()) {
            deviceName = device.getAddress();
        }
        holder.tvDeviceName.setText(deviceName);
        
        // 设置设备MAC地址
        holder.tvDeviceAddress.setText(device.getAddress());
        
        // 设置信号强度
        holder.tvSignalStrength.setText(String.format("%d dBm", rssi));
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDeviceClick(device);
            }
        });
    }

    @Override
    public int getItemCount() {
        return deviceList.size();
    }

    /**
     * 添加设备到列表
     * @param device 蓝牙设备
     * @param rssi 信号强度
     */
    public void addDevice(BluetoothDevice device, int rssi) {
        // 检查设备是否已在列表中
        for (int i = 0; i < deviceList.size(); i++) {
            if (deviceList.get(i).getAddress().equals(device.getAddress())) {
                // 更新信号强度
                rssiList.set(i, rssi);
                notifyItemChanged(i);
                return;
            }
        }
        
        // 添加新设备
        deviceList.add(device);
        rssiList.add(rssi);
        notifyItemInserted(deviceList.size() - 1);
    }

    /**
     * 清空设备列表
     */
    public void clearDevices() {
        deviceList.clear();
        rssiList.clear();
        notifyDataSetChanged();
    }

    /**
     * 获取设备列表
     */
    public List<BluetoothDevice> getDevices() {
        return new ArrayList<>(deviceList);
    }

    static class DeviceViewHolder extends RecyclerView.ViewHolder {
        TextView tvDeviceName;
        TextView tvDeviceAddress;
        TextView tvSignalStrength;

        DeviceViewHolder(View itemView) {
            super(itemView);
            tvDeviceName = itemView.findViewById(R.id.tv_ble_device_name);
            tvDeviceAddress = itemView.findViewById(R.id.tv_ble_device_address);
            tvSignalStrength = itemView.findViewById(R.id.tv_ble_device_rssi);
        }
    }
} 