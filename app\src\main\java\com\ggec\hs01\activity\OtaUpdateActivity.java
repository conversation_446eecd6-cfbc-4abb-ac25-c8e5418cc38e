package com.ggec.hs01.activity;

import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.GGECHSApplication;
import com.ggec.hs01.R;
import com.ggec.hs01.view.HMButton;
import com.ggec.yotasdk.YOTAApi;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * OTA固件升级页面
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * 提供OTA固件升级功能
 * 允许用户通过OTA升级耳机的固件
 */
public class OtaUpdateActivity extends AppCompatActivity {

    private static final String TAG = "OtaUpdateActivity";

    // API实例
    private YoCommandApi commandApi;
    private YoBLEApi bleApi;
    private YOTAApi yotaApi;

    // UI组件
    private TextView tvOtaProgress;
    private TextView tvOtaStatus;
    private HMButton btnStartOta;
    private HMButton btnCheckOtaStatus;
    private HMButton btnCheckFirmware;

    // Handler用于延迟任务
    private Handler handler = new Handler();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_ota_update);
        
        // 初始化日志标签
        Log.d(TAG, "OtaUpdateActivity onCreate");
        
        // 获取API实例
        commandApi = YoCommandApi.getInstance();
        bleApi = YoBLEApi.getInstance();
        
        // 通过应用程序类获取YOTA API实例
        GGECHSApplication app = (GGECHSApplication) getApplication();
        yotaApi = app.getOtaApi();
        
        if (yotaApi == null) {
            Log.e(TAG, "YOTAApi实例初始化失败");
            Toast.makeText(this, "OTA系统初始化失败", Toast.LENGTH_LONG).show();
        }
        
        // 初始化视图
        initViews();
        
        // 初始化返回按钮
        initBackButton();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.ota_update_main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        
        // 设置OTA进度和状态监听器
        setupOtaListeners();
        
        // 设置点击事件监听器
        setupClickListeners();
        
        // 初始状态
        updateUIState(bleApi.isDeviceConnected());
        
        // 测试阶段：使OTA按钮始终可用
        btnStartOta.setEnabled(true);
        Log.d(TAG, "OTA按钮已启用");
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 页面恢复时，重新确保OTA按钮可点击
        btnStartOta.setEnabled(true);
        
        // 检查BLE连接状态
        updateUIState(bleApi != null && bleApi.isDeviceConnected());
    }
    
    /**
     * 设置OTA监听器
     */
    private void setupOtaListeners() {
        // 设置进度监听器
        yotaApi.setProgressListener(new YOTAApi.ProgressListener() {
            @Override
            public void onProgressChanged(float progress) {
                int progressInt = (int) progress;
                Log.d(TAG, "OTA升级进度: " + progressInt + "%");
                runOnUiThread(() -> {
                    tvOtaProgress.setText(progressInt + "%");
                });
            }
        });
        
        // 设置状态监听器
        yotaApi.setStatusListener(new YOTAApi.StatusListener() {
            @Override
            public void onStatusChanged(YOTAApi.Status status) {
                runOnUiThread(() -> {
                    Log.d(TAG, "OTA状态变化: " + status);
                    
                    // 更新UI显示状态文本
                    tvOtaStatus.setText(status.getName());
                    
                    // 根据状态更新按钮可用性
                    boolean isCompleted = (status == YOTAApi.Status.SUCCEED ||
                                           status == YOTAApi.Status.FAILED ||
                                           status == YOTAApi.Status.CANCELED);
                    
                    if (isCompleted) {
                        // 升级结束后恢复按钮状态
                        btnStartOta.setEnabled(true);
                    }
                });
            }
            
            @Override
            public void onError(int errorCode, String message) {
                runOnUiThread(() -> {
                    Log.e(TAG, "OTA错误: " + errorCode + ", " + message);
                    tvOtaStatus.setText("错误: " + message);
                    Toast.makeText(OtaUpdateActivity.this, "OTA错误: " + message, Toast.LENGTH_LONG).show();
                    
                    // 错误后恢复按钮状态
                    btnStartOta.setEnabled(true);
                });
            }
            
            @Override
            public void onSuccess() {
                runOnUiThread(() -> {
                    Log.d(TAG, "OTA升级成功完成");
                    Toast.makeText(OtaUpdateActivity.this, "OTA升级成功", Toast.LENGTH_LONG).show();
                    
                    // 升级成功后恢复按钮状态
                    btnStartOta.setEnabled(true);
                });
            }
        });
    }
    
    /**
     * 设置点击事件监听器
     */
    private void setupClickListeners() {
        // OTA状态检测按钮点击事件
        btnCheckOtaStatus.setOnClickListener(v -> {
            // 设置按钮为不可点击，显示检测中状态
            btnCheckOtaStatus.setEnabled(false);
            tvOtaStatus.setText("检测中...");
            
            // 调用修改后的OTA状态检测API
            bleApi.IsitCanOTA(new YoBLEApi.IsitCanOtaCallback() {
                @Override
                public void onResult(boolean canOta) {
                    // 更新UI显示
                    runOnUiThread(() -> {
                        // 恢复按钮可点击状态
                        btnCheckOtaStatus.setEnabled(true);
                        
                        // 更新OTA状态 - 只显示是否可以进行OTA
                        if (canOta) {
                            tvOtaStatus.setText("设备已准备好，可以进行OTA升级");
                            tvOtaStatus.setTextColor(getResources().getColor(R.color.success_green, null));
                        } else {
                            tvOtaStatus.setText("设备未准备好，无法进行OTA升级");
                            tvOtaStatus.setTextColor(getResources().getColor(R.color.error_red, null));
                        }
                    });
                }
            });
        });
        
        // OTA升级按钮点击事件
        btnStartOta.setOnClickListener(v -> {
            // 开始OTA升级
            startOtaUpgrade();
        });

        // 查看固件文件按钮点击事件
        btnCheckFirmware.setOnClickListener(v -> {
            checkFirmwareFiles();
        });
    }
    
    /**
     * 查看固件文件
     */
    private void checkFirmwareFiles() {
        File externalFilesDir = getExternalFilesDir(null);
        if (externalFilesDir != null) {
            Log.d(TAG, "目标文件夹路径: " + externalFilesDir.getAbsolutePath());
            File[] files = externalFilesDir.listFiles();
            if (files != null && files.length > 0) {
                Log.d(TAG, "固件文件列表:");
                for (File file : files) {
                    Log.d(TAG, "文件名: " + file.getName());
                    if (file.getName().toLowerCase().endsWith(".bin")) {
                        printBinFileContent(file);
                    }
                }
                Toast.makeText(this, "文件列表已打印到Logcat", Toast.LENGTH_SHORT).show();
            } else {
                Log.d(TAG, "文件夹为空或无法读取");
                Toast.makeText(this, "文件夹为空或无法读取", Toast.LENGTH_SHORT).show();
            }
        } else {
            Log.e(TAG, "无法访问外部存储文件夹");
            Toast.makeText(this, "无法访问外部存储文件夹", Toast.LENGTH_SHORT).show();
        }
    }

    private void printBinFileContent(File file) {
        try {
            long fileSize = file.length();
            Log.d(TAG, "正在读取 " + file.getName() + ", 大小: " + fileSize + " bytes");

            FileInputStream fis = new FileInputStream(file);
            int bytesToRead = (int) Math.min(fileSize, 1024); // 最多读取1KB
            byte[] buffer = new byte[bytesToRead];
            int bytesRead = fis.read(buffer);
            fis.close();

            if (bytesRead > 0) {
                byte[] data = new byte[bytesRead];
                System.arraycopy(buffer, 0, data, 0, bytesRead);
                String hexString = bytesToHex(data);
                Log.d(TAG, "文件内容 (前 " + bytesRead + " 字节):");
                Log.d(TAG, hexString);
            } else {
                Log.d(TAG, file.getName() + " 是空的.");
            }
        } catch (IOException e) {
            Log.e(TAG, "读取文件失败: " + file.getName(), e);
        }
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString();
    }
    
    /**
     * 开始OTA升级
     */
    private void startOtaUpgrade() {
        Log.d(TAG, "尝试开始OTA升级");
        tvOtaProgress.setText("0%");

        boolean result = yotaApi.startUpgrade();
        if (result) {
            btnStartOta.setEnabled(false);
            Toast.makeText(this, "开始OTA升级", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "OTA升级已开始");
        } else {
            Toast.makeText(this, "启动OTA升级失败，请检查设备状态或日志", Toast.LENGTH_SHORT).show();
            Log.e(TAG, "OTA升级启动失败");
        }
    }
    
    /**
     * 更新UI状态
     * @param isConnected 是否已连接设备
     */
    private void updateUIState(boolean isConnected) {
        if (isConnected) {
            // 已连接状态
            btnCheckOtaStatus.setEnabled(true); // 连接成功后，启用状态检测按钮
        } else {
            // 未连接状态
            btnCheckOtaStatus.setEnabled(false); // 未连接时，禁用状态检测按钮
            
            // 重置OTA进度显示
            tvOtaProgress.setText("0%");
            
            // 重置OTA状态显示
            tvOtaStatus.setText("设备未连接");
            tvOtaStatus.setTextColor(getResources().getColor(R.color.error_red, null));
        }
    }
    
    /**
     * 初始化视图组件
     */
    private void initViews() {
        tvOtaProgress = findViewById(R.id.tv_ota_progress);
        tvOtaStatus = findViewById(R.id.tv_ota_status);
        btnStartOta = findViewById(R.id.btn_start_ota);
        btnCheckOtaStatus = findViewById(R.id.btn_check_ota_status);
        btnCheckFirmware = findViewById(R.id.btn_check_firmware);
    }
    
    /**
     * 初始化返回按钮
     */
    private void initBackButton() {
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // 移除所有挂起的Handler回调，防止内存泄漏
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        
        // 如果正在进行OTA升级，取消升级
        if (yotaApi != null) {
            yotaApi.cancelUpgrade();
        }
        
        // 清除持有的引用
        commandApi = null;
        bleApi = null;
        yotaApi = null;
    }
} 