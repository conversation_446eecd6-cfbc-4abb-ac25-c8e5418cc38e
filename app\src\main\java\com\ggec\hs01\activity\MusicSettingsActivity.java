package com.ggec.hs01.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.view.HMSwitch;
import android.util.Log;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * 音乐设置界面
 * 允许用户自定义耳机的音乐设置
 */
public class MusicSettingsActivity extends AppCompatActivity {
    private static final String TAG = "MusicSettingsActivity";
    
    // 音量自适应开关
    private HMSwitch switchVolumeAdaptive;
    
    // 音质模式单选按钮组
    private RadioGroup rgSoundQualityMode;
    private RadioButton rbHighQuality;
    private RadioButton rbHighEndurance;
    
    // EQ模式单选按钮组
    private RadioGroup rgEQMode;
    private RadioButton rbMusicMode;
    private RadioButton rbMovieMode;
    private RadioButton rbNewsMode;
    private RadioButton rbElderlyMode;
    
    // 听力保护功能开关
    private HMSwitch switchVolumeReminder;
    
    // 命令API
    private YoCommandApi commandApi;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_music_settings);
        
        // 获取YoBLEApi实例
        bleApi = YoBLEApi.getInstance();
        
        // 获取命令API实例
        commandApi = YoCommandApi.getInstance();
        
        // 初始化返回按钮
        initBackButton();
        
        // 初始化音量自适应开关
        initVolumeAdaptiveSwitch();
        
        // 初始化音质模式
        initSoundQualityMode();
        
        // 初始化EQ模式
        initEQMode();
        
        // 初始化听力保护功能
        initHearingProtection();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.music_settings_main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
    }
    
    @Override
    protected void onStart() {
        super.onStart();
        
        // 确保已连接设备，如果没有则返回连接页面
        if (!bleApi.isDeviceConnected()) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    @Override
    protected void onStop() {
        super.onStop();
        // 不需要额外操作
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
    
    /**
     * 初始化返回按钮
     */
    private void initBackButton() {
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
    }
    
    /**
     * 初始化音量自适应开关
     */
    private void initVolumeAdaptiveSwitch() {
        switchVolumeAdaptive = findViewById(R.id.switch_volume_adaptive);
        
        // 设置默认状态（默认关闭）
        switchVolumeAdaptive.setChecked(false);
        
        // 设置点击监听
        switchVolumeAdaptive.setOnCheckedChangeListener(new HMSwitch.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch switchView, boolean isChecked) {
                Log.i(TAG, "音量自适应开关状态: " + isChecked);
                
                // 使用命令API发送命令
                commandApi.setVolumeAdaptive(isChecked, (code) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(MusicSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                            switchVolumeAdaptive.setChecked(!isChecked);
                        }
                    });
                });
            }
        });
    }
    
    /**
     * 初始化音质模式
     */
    private void initSoundQualityMode() {
        rgSoundQualityMode = findViewById(R.id.rg_sound_quality_mode);
        rbHighQuality = findViewById(R.id.rb_high_quality);
        rbHighEndurance = findViewById(R.id.rb_high_endurance);
        
        // 设置默认状态（默认高音质模式）
        rbHighQuality.setChecked(true);
        
        // 设置选择监听
        rgSoundQualityMode.setOnCheckedChangeListener((group, checkedId) -> {
            boolean isHighQuality = checkedId == R.id.rb_high_quality;
            Log.i(TAG, "音质模式选择: " + (isHighQuality ? "高音质" : "高续航"));
            
            // 使用命令API发送命令
            commandApi.setSoundQualityMode(isHighQuality, (code) -> {
                runOnUiThread(() -> {
                    // 显示结果消息
                    String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                            "设置成功" : "设置失败";
                    Toast.makeText(MusicSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                    
                    // 如果失败，恢复选择状态
                    if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                        rbHighQuality.setChecked(!isHighQuality);
                        rbHighEndurance.setChecked(isHighQuality);
                    }
                });
            });
        });
    }
    
    /**
     * 初始化EQ模式
     */
    private void initEQMode() {
        rgEQMode = findViewById(R.id.rg_eq_mode);
        rbMusicMode = findViewById(R.id.rb_music_mode);
        rbMovieMode = findViewById(R.id.rb_movie_mode);
        rbNewsMode = findViewById(R.id.rb_news_mode);
        rbElderlyMode = findViewById(R.id.rb_human_voice_mode);
        
        // 设置默认状态（默认音乐模式）
        rbMusicMode.setChecked(true);
        
        // 设置选择监听
        rgEQMode.setOnCheckedChangeListener((group, checkedId) -> {
            int mode;
            if (checkedId == R.id.rb_movie_mode) {
                mode = YoCommandApi.EQ_MOVIE_MODE; // 1
            } else if (checkedId == R.id.rb_news_mode) {
                mode = YoCommandApi.EQ_NEWS_MODE; // 2
            } else if (checkedId == R.id.rb_music_mode) {
                mode = YoCommandApi.EQ_MUSIC_MODE; // 0
            } else if (checkedId == R.id.rb_human_voice_mode) {
                mode = YoCommandApi.EQ_ELDERLY_MODE; // 现在是3，之前是4
            } else {
                mode = YoCommandApi.EQ_MUSIC_MODE; // 默认音乐模式
            }
            
            Log.i(TAG, "EQ模式选择: " + mode);
            
            // 保存当前选择状态，用于失败时恢复
            final RadioButton currentChecked = findViewById(checkedId);
            
            // 使用命令API发送命令
            commandApi.setEQMode(mode, (code) -> {
                runOnUiThread(() -> {
                    // 显示结果消息
                    String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                            "设置成功" : "设置失败";
                    Toast.makeText(MusicSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                    
                    // 如果失败，恢复选择状态
                    if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                        // 简单地重置为音乐模式
                        rbMusicMode.setChecked(true);
                    }
                });
            });
        });
    }
    
    /**
     * 初始化听力保护功能
     */
    private void initHearingProtection() {
        // 初始化音量提醒开关
        switchVolumeReminder = findViewById(R.id.switch_volume_reminder);
        switchVolumeReminder.setChecked(false); // 默认关闭
        
        // 设置音量提醒开关点击监听
        switchVolumeReminder.setOnCheckedChangeListener(new HMSwitch.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch switchView, boolean isChecked) {
                Log.i(TAG, "音量提醒开关状态: " + isChecked);
                
                // 使用命令API发送命令
                commandApi.setVolumeRemind(isChecked, (code) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(MusicSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                            switchVolumeReminder.setChecked(!isChecked);
                        }
                    });
                });
            }
        });
        

    }
} 