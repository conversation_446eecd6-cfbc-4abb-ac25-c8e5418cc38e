<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bluetooth_connect_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.BluetoothConnectActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/comp_background_gray"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="蓝牙连接"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 刷新按钮 -->
        <ImageView
            android:id="@+id/btn_refresh"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_refresh"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 状态提示文本 -->
    <TextView
        android:id="@+id/tv_scan_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:text="正在搜索蓝牙设备..."
        android:textColor="@color/font_secondary"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar" />

    <!-- 蓝牙设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_bluetooth_devices"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progress_scanning"
        tools:listitem="@layout/item_ble_device" />

    <!-- 无设备提示 -->
    <TextView
        android:id="@+id/tv_no_devices"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="未发现蓝牙设备"
        android:textColor="@color/font_primary"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_scanning"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:indeterminate="true"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_scan_status" />

</androidx.constraintlayout.widget.ConstraintLayout> 