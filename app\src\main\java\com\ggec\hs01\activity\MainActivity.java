package com.ggec.hs01.activity;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.GGECHSApplication;
import com.ggec.hs01.view.HMButton;
import com.ggec.hs01.view.HMSwitch;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * 应用主界面
 * 提供蓝牙连接和各种设置功能
 */
public class MainActivity extends AppCompatActivity {
    
    private static final String TAG = "MainActivity";
    private static final int REQUEST_CONNECT_DEVICE = 1;
    private static final int REQUEST_PERMISSIONS_CODE = 101;
    
    // 蓝牙状态和电量显示
    private TextView tvBluetoothStatus;
    private TextView tvBatteryLeft;
    private TextView tvBatteryRight;
    private TextView tvBatteryCase;
    private TextView tvCaseChargingStatus;
    
    // 电池监听开关
    private HMSwitch switchBatteryMonitor;
    
    // 设置卡片
    private CardView cardMusicSettings;
    private CardView cardControlSettings;
    private CardView cardFunctionSettings;
    private CardView cardAppSettings;
    
    // 蓝牙相关
    private BluetoothDevice connectedDevice;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;
    
    // YoCommandApi 实例
    private YoCommandApi commandApi;
    
    // 主线程Handler
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    // 标记是否已获取过初始数据
    private boolean hasInitialDataFetched = false;
    
    // 标记电池自动监听是否已启动
    private boolean isBatteryMonitorStarted = false;
    
    // 蓝牙事件监听器
    private final YoBLEApi.BleListener bleListener = new YoBLEApi.BleListener() {
        @Override
        public void onTestDataSent(String data) {
            // 移除测试数据相关功能
        }
        
        @Override
        public void onTestDataReceived(String data) {
            // 移除测试数据相关功能
        }
        
        @Override
        public void onError(String errorMsg) {
            showMessage(errorMsg);
        }

        @Override
        public void onConnectionStateChanged(BluetoothDevice device, boolean connected) {
            runOnUiThread(() -> {
                if (connected) {
                    connectedDevice = device;
                    updateBluetoothStatus("已连接: " + device.getName());
                    Log.i(TAG, "设备连接成功...");
                    
                    // 如果自动监听开关已打开，则启动监听
                    if (switchBatteryMonitor != null && switchBatteryMonitor.isChecked()) {
                        startBatteryMonitor();
                    }
                } else {
                    connectedDevice = null;
                    updateBluetoothStatus("蓝牙未连接");
                    // 断开连接后重置电量显示
                    updateBatteryLevels(0, 0, 0);
                    // 直接重置充电盒充电状态显示，不调用获取命令
                    tvCaseChargingStatus.setText("");
                    // 重置标志位
                    hasInitialDataFetched = false;
                    
                    // 停止电池监听
                    stopBatteryMonitor();
                    
                    // 重置监听开关状态
                    if (switchBatteryMonitor != null) {
                        switchBatteryMonitor.setChecked(false);
                    }
                }
            });
        }
        
        @Override
        public void onDeviceReadyStateChanged(boolean ready) {
            // 不再在设备就绪时自动获取电池信息
            if (ready && connectedDevice != null) {
                Log.i(TAG, "设备就绪");
                
                // 如果自动监听开关已打开，则启动监听
                runOnUiThread(() -> {
                    if (switchBatteryMonitor != null && switchBatteryMonitor.isChecked()) {
                        startBatteryMonitor();
                    }
                });
            }
        }
    };

    // 命令回调，用于在命令执行完成后通知用户
    private final YoCommandApi.CommandResultCallback commandResultCallback = code -> {
        Log.i(TAG, "命令执行结果: " + (code == YoCommandApi.CommandResultCode.SUCCESS ? "成功" : "失败"));
    };
    
    // 电池自动监听回调
    private final YoCommandApi.BatteryAutoMonitorCallback batteryMonitorCallback = new YoCommandApi.BatteryAutoMonitorCallback() {
        @Override
        public void onBatteryInfoUpdated(int code, int leftLevel, int rightLevel, int caseLevel) {
            runOnUiThread(() -> {
                updateBatteryLevels(leftLevel, rightLevel, caseLevel);
                Log.d(TAG, "电池电量自动更新: 左耳=" + leftLevel + "%, 右耳=" + rightLevel + "%, 盒子=" + caseLevel + "%");
            });
        }
        
        @Override
        public void onCaseChargingStatusUpdated(int code, boolean isCharging) {
            runOnUiThread(() -> {
                updateCaseChargingStatus(isCharging);
                Log.d(TAG, "充电盒充电状态自动更新: " + (isCharging ? "充电中" : "未充电"));
            });
        }
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        // 在Activity创建时检查并请求权限
        checkAndRequestPermissions();
        
        // 获取YoBLEApi实例
        bleApi = ((GGECHSApplication) getApplication()).getBleApi();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        
        // 初始化蓝牙连接按钮
        initBluetoothButton();
        
        // 初始化电池监听开关
        initBatteryMonitorSwitch();
        
        // 初始化获取状态按钮
        initGetStatusButton();
        
        // 初始化蓝牙状态和电量显示
        initBluetoothStatus();
        
        // 初始化设置卡片
        initSettingsCards();
        
        // 额外设置开关的尺寸
        View switchTrack = switchBatteryMonitor.findViewById(android.R.id.background);
        View switchThumb = switchBatteryMonitor.findViewById(android.R.id.toggle);
        if (switchTrack != null) {
            ViewGroup.LayoutParams params = switchTrack.getLayoutParams();
            if (params != null && params.width > 0) {
                params.width = (int)(params.width * 0.8f);
                switchTrack.setLayoutParams(params);
            }
        }
    }
    
    /**
     * 检查并请求必要的运行时权限。
     * 即使用户拒绝，App也能继续运行。
     */
    private void checkAndRequestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            List<String> permissionsToRequest = new ArrayList<>();
            String[] requiredPermissions;

            // 根据Android版本确定需要请求的权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) { // Android 13 (API 33)
                requiredPermissions = new String[]{
                        Manifest.permission.READ_MEDIA_IMAGES,
                        Manifest.permission.READ_MEDIA_VIDEO,
                        Manifest.permission.READ_MEDIA_AUDIO,
                        Manifest.permission.READ_PHONE_STATE
                };
            } else { // Android 6 ~ 12
                requiredPermissions = new String[]{
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.READ_PHONE_STATE
                };
            }

            for (String permission : requiredPermissions) {
                if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                    permissionsToRequest.add(permission);
                }
            }

            if (!permissionsToRequest.isEmpty()) {
                ActivityCompat.requestPermissions(this,
                        permissionsToRequest.toArray(new String[0]),
                        REQUEST_PERMISSIONS_CODE);
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSIONS_CODE) {
            for (int i = 0; i < permissions.length; i++) {
                if (grantResults[i] == PackageManager.PERMISSION_GRANTED) {
                    Log.i(TAG, "权限 " + permissions[i] + " 已被授予。");
                } else {
                    Log.w(TAG, "权限 " + permissions[i] + " 已被拒绝。");
                    Toast.makeText(this, "权限 " + permissions[i].substring(permissions[i].lastIndexOf(".") + 1) + " 被拒绝，相关功能可能无法使用。", Toast.LENGTH_SHORT).show();
                }
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        
        // 注册蓝牙监听器
        bleApi.registerListener(bleListener);
        
        // 检查是否有已连接的设备
        connectedDevice = bleApi.getConnectedDevice();
        if (connectedDevice != null) {
            updateBluetoothStatus("已连接: " + connectedDevice.getName());
            
            // 仅更新蓝牙状态，不自动获取电量
            if (!bleApi.isDeviceReady()) {
                Log.i(TAG, "设备已连接但未就绪");
            } else {
                // 设备已就绪，如果自动监听开关已打开，则启动监听
                if (switchBatteryMonitor != null && switchBatteryMonitor.isChecked()) {
                    startBatteryMonitor();
                }
            }
        } else {
            updateBluetoothStatus("蓝牙未连接");
            
            // 重置监听开关状态
            if (switchBatteryMonitor != null) {
                switchBatteryMonitor.setChecked(false);
            }
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        
        // 注销蓝牙监听器
        bleApi.unregisterListener(bleListener);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 不在此断开蓝牙连接，只有在应用退出时才断开
        // 停止电池监听
        stopBatteryMonitor();
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CONNECT_DEVICE && resultCode == RESULT_OK) {
            // 连接成功，但不立即获取电量信息，等待设备就绪
            Log.i(TAG, "蓝牙连接流程完成，等待设备就绪...");
        }
    }
    
    /**
     * 初始化蓝牙连接按钮
     */
    private void initBluetoothButton() {
        HMButton btnCustom = findViewById(R.id.btn_custom);
        btnCustom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 直接进入蓝牙连接页面
                Intent intent = new Intent(MainActivity.this, BluetoothConnectActivity.class);
                startActivityForResult(intent, REQUEST_CONNECT_DEVICE);
            }
        });
    }
    
    /**
     * 初始化电池监听开关
     */
    private void initBatteryMonitorSwitch() {
        switchBatteryMonitor = findViewById(R.id.switch_battery_monitor);
        switchBatteryMonitor.setOnCheckedChangeListener(new HMSwitch.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch switchView, boolean isChecked) {
                // 检查设备是否连接和就绪
                if (isChecked) {
                    if (connectedDevice == null) {
                        Toast.makeText(MainActivity.this, "蓝牙未连接，无法开启监听", Toast.LENGTH_SHORT).show();
                        switchView.setChecked(false);
                        return;
                    }
                    
                    if (!bleApi.isDeviceReady()) {
                        Toast.makeText(MainActivity.this, "设备未就绪，无法开启监听", Toast.LENGTH_SHORT).show();
                        switchView.setChecked(false);
                        return;
                    }
                    
                    // 启动电池监听
                    boolean success = startBatteryMonitor();
                    if (!success) {
                        Toast.makeText(MainActivity.this, "开启电池监听失败", Toast.LENGTH_SHORT).show();
                        switchView.setChecked(false);
                    } else {
                        Toast.makeText(MainActivity.this, "已开启电池监听", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    // 停止电池监听
                    stopBatteryMonitor();
                    Toast.makeText(MainActivity.this, "已停止电池监听", Toast.LENGTH_SHORT).show();
                }
            }
        });
        
        // 设置初始状态为关闭
        switchBatteryMonitor.setChecked(false);
        switchBatteryMonitor.setEnabled(true);
    }
    
    /**
     * 初始化获取状态按钮
     */
    private void initGetStatusButton() {
        HMButton btnGetStatus = findViewById(R.id.btn_get_status);
        btnGetStatus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 主动获取首页聚合状态
                getHomePageStatus();
            }
        });
    }
    
    /**
     * 初始化蓝牙状态和电量显示
     */
    private void initBluetoothStatus() {
        tvBluetoothStatus = findViewById(R.id.tv_bluetooth_status);
        tvBatteryLeft = findViewById(R.id.tv_battery_left);
        tvBatteryRight = findViewById(R.id.tv_battery_right);
        tvBatteryCase = findViewById(R.id.tv_battery_case);
        tvCaseChargingStatus = findViewById(R.id.tv_case_charging_status);
        
        // 获取YoCommandApi实例
        commandApi = ((GGECHSApplication) getApplication()).getCommandApi();
        
        // 显示蓝牙未连接状态
        updateBluetoothStatus("蓝牙未连接");
        updateBatteryLevels(0, 0, 0);
        updateCaseChargingStatus(false);
    }
    
    /**
     * 更新蓝牙状态显示
     */
    private void updateBluetoothStatus(String status) {
        if (tvBluetoothStatus != null) {
            tvBluetoothStatus.setText(status);
        }
    }
    
    /**
     * 更新电池电量显示
     */
    private void updateBatteryLevels(int leftLevel, int rightLevel, int caseLevel) {
        // 已在调用处使用runOnUiThread，此处无需再次使用
        if (tvBatteryLeft != null) {
            if (leftLevel > 0) {
                tvBatteryLeft.setText(leftLevel + "%");
            } else {
                tvBatteryLeft.setText("--%");
            }
        }
        
        if (tvBatteryRight != null) {
            if (rightLevel > 0) {
                tvBatteryRight.setText(rightLevel + "%");
            } else {
                tvBatteryRight.setText("--%");
            }
        }
        
        if (tvBatteryCase != null) {
            if (caseLevel > 0) {
                tvBatteryCase.setText(caseLevel + "%");
            } else {
                tvBatteryCase.setText("--%");
            }
        }
    }
    
    /**
     * 初始化设置卡片
     */
    private void initSettingsCards() {
        cardMusicSettings = findViewById(R.id.card_music_settings);
        cardControlSettings = findViewById(R.id.card_control_settings);
        cardFunctionSettings = findViewById(R.id.card_function_settings);
        cardAppSettings = findViewById(R.id.card_app_settings);
        
        // 设置点击事件
        cardMusicSettings.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, MusicSettingsActivity.class);
            startActivity(intent);
        });
        
        cardControlSettings.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, ControlSettingsActivity.class);
            startActivity(intent);
        });
        
        cardFunctionSettings.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, FunctionSettingsActivity.class);
            startActivity(intent);
        });
        
        cardAppSettings.setOnClickListener(v -> {
            // 检查是否已连接设备
            if (connectedDevice != null) {
                Intent intent = new Intent(MainActivity.this, AppSettingsActivity.class);
                startActivity(intent);
            } else {
                // 未连接设备时显示提示信息
                showMessage("蓝牙未连接");
            }
        });
    }
    
    /**
     * 显示消息
     * @param message 消息内容
     */
    private void showMessage(final String message) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                tvBluetoothStatus.setText(message);
            }
        });
    }
    
    /**
     * 更新充电盒充电状态显示
     */
    private void updateCaseChargingStatus(boolean isCharging) {
        if (tvCaseChargingStatus != null) {
            if (isCharging) {
                tvCaseChargingStatus.setText("充电中");
            } else {
                tvCaseChargingStatus.setText("");
            }
        }
    }
    
    /**
     * 获取首页聚合状态
     * 用于手动获取电量、充电状态、固件版本等信息
     */
    private void getHomePageStatus() {
        // 确保设备已连接且命令API可用
        if (connectedDevice == null || commandApi == null) {
            showMessage("设备未连接，无法获取状态");
            return;
        }

        // 确保设备处于就绪状态
        if (!bleApi.isDeviceReady()) {
            showMessage("设备未就绪，无法获取状态");
            return;
        }

        Log.i(TAG, "手动获取首页聚合状态");
        showMessage("正在获取首页聚合状态...");

        try {
            // 使用YoCommandApi获取首页聚合状态
            commandApi.getHomePageStatus(new YoCommandApi.HomePageStatusCallback() {
                @Override
                public void onHomePageStatusResult(int code, Map<String, String> statusMap) {
                    if (code == YoCommandApi.CommandResultCode.SUCCESS || !statusMap.isEmpty()) {
                        // 即使状态码不是成功，只要有数据就认为部分成功
                        String resultStatus = (code == YoCommandApi.CommandResultCode.SUCCESS) ? "完全成功" : "部分成功";
                        
                        // 采用多行格式记录日志
                        StringBuilder logMsg = new StringBuilder("首页聚合状态获取" + resultStatus + ":\n");
                        if (!statusMap.isEmpty()) {
                            logMsg.append("{\n");
                            List<String> sortedKeys = new ArrayList<>(statusMap.keySet());
                            Collections.sort(sortedKeys);
                            for (String key : sortedKeys) {
                                logMsg.append("    ").append(key).append(" = ").append(statusMap.get(key)).append(";\n");
                            }
                            logMsg.append("}");
                        }
                        Log.i(TAG, logMsg.toString());

                        runOnUiThread(() -> {
                            // 更新UI显示
                            updateUIFromAggregatedStatus(statusMap);
                            
                            Toast.makeText(MainActivity.this, 
                                    code == YoCommandApi.CommandResultCode.SUCCESS ? 
                                    "首页聚合状态已更新" : "首页状态部分更新", 
                                    Toast.LENGTH_SHORT).show();
                                    
                            // 恢复蓝牙状态显示
                            if (connectedDevice != null) {
                                updateBluetoothStatus("已连接: " + connectedDevice.getName());
                            }
                        });
                    } else {

                        runOnUiThread(() -> {
                            //showMessage("获取首页聚合状态失败，请重试");
                        });
                    }
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "获取首页聚合状态失败", e);
            showMessage("获取首页聚合状态失败，请重试");
        }
    }
    
    /**
     * 根据聚合状态数据更新UI
     * @param statusMap 聚合状态数据
     */
    private void updateUIFromAggregatedStatus(Map<String, String> statusMap) {
        // 从状态Map中提取电量信息并更新UI
        try {
            // 左耳电量
            if (statusMap.containsKey("80_1")) {
                int leftLevel = Integer.parseInt(statusMap.get("80_1"));
                if (leftLevel > 0) {
                    tvBatteryLeft.setText(leftLevel + "%");
                }
            }
            
            // 右耳电量
            if (statusMap.containsKey("80_2")) {
                int rightLevel = Integer.parseInt(statusMap.get("80_2"));
                if (rightLevel > 0) {
                    tvBatteryRight.setText(rightLevel + "%");
                }
            }
            
            // 盒子电量
            if (statusMap.containsKey("80_3")) {
                int caseLevel = Integer.parseInt(statusMap.get("80_3"));
                if (caseLevel > 0) {
                    tvBatteryCase.setText(caseLevel + "%");
                }
            }
            
            // 充电盒充电状态
            if (statusMap.containsKey("95")) {
                boolean isCharging = "01".equals(statusMap.get("95"));
                updateCaseChargingStatus(isCharging);
            }
        } catch (Exception e) {
            Log.e(TAG, "处理聚合状态数据出错", e);
        }
    }
    
    /**
     * 启动电池监听
     * @return 是否成功启动监听
     */
    private boolean startBatteryMonitor() {
        // 如果已经在监听，不要重复启动
        if (isBatteryMonitorStarted) {
            return true;
        }
        
        // 确保设备已连接且命令API可用
        if (connectedDevice == null || commandApi == null) {
            Log.e(TAG, "设备未连接或命令API不可用，无法启动电池监听");
            return false;
        }
        
        // 确保设备处于就绪状态
        if (!bleApi.isDeviceReady()) {
            Log.e(TAG, "设备未就绪，无法启动电池监听");
            return false;
        }
        
        try {
            // 启动电池监听
            boolean success = commandApi.startBatteryMonitor(batteryMonitorCallback);
            if (success) {
                Log.i(TAG, "电池监听已启动");
                isBatteryMonitorStarted = true;
                return true;
            } else {
                Log.e(TAG, "启动电池监听失败");
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "启动电池监听出错", e);
            return false;
        }
    }
    
    /**
     * 停止电池监听
     */
    private void stopBatteryMonitor() {
        // 如果没有在监听，不需要停止
        if (!isBatteryMonitorStarted) {
            return;
        }
        
        // 停止电池监听
        if (commandApi != null) {
            commandApi.stopBatteryMonitor();
            Log.i(TAG, "电池监听已停止");
        }
        
        isBatteryMonitorStarted = false;
    }
} 