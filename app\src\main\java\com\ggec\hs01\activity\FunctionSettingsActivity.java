package com.ggec.hs01.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.view.HMSwitch;
import com.ggec.hs01.GGECHSApplication;
import android.util.Log;

import com.ggec.hs01.view.HMSwitch.OnCheckedChangeListener;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * 功能设置界面
 * 允许用户自定义耳机的功能设置
 */
public class FunctionSettingsActivity extends AppCompatActivity {
    private static final String TAG = "FunctionSettingsActivity";
    
    // 添加YoCommandApi变量
    private YoCommandApi commandApi;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;

    // 界面控件
    private HMSwitch switchWearDetection;
    private HMSwitch switchVadWakeup;
    private HMSwitch switchGameMode;
    private HMSwitch switchFallAlert;
    private HMSwitch switchEarLocation;
    private HMSwitch switchFindAlert;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_function_settings);
        
        // 获取YoBLEApi实例
        bleApi = ((GGECHSApplication) getApplication()).getBleApi();
        
        // 获取YoCommandApi实例
        commandApi = ((GGECHSApplication) getApplication()).getCommandApi();
        
        // 初始化返回按钮
        initBackButton();
        
        // 初始化界面控件
        initViews();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.function_settings_main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
    }
    
    @Override
    protected void onStart() {
        super.onStart();
        
        // 确保已连接设备，如果没有则返回连接页面
        if (!bleApi.isDeviceConnected()) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    @Override
    protected void onStop() {
        super.onStop();
        // 不需要额外操作
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 不需要额外操作
    }
    
    /**
     * 初始化返回按钮
     */
    private void initBackButton() {
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
    }
    
    /**
     * 初始化界面控件
     */
    private void initViews() {
        // 佩戴检测开关
        switchWearDetection = findViewById(R.id.switch_wear_detection);
        switchWearDetection.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "佩戴检测开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置佩戴检测
                commandApi.setWearDetection(isChecked, (code) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                            switchWearDetection.setChecked(!isChecked);
                        }
                    });
                });
            }
        });
        
        // 语音唤醒开关
        switchVadWakeup = findViewById(R.id.switch_vad_wakeup);
        switchVadWakeup.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "语音唤醒开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置语音唤醒
                commandApi.setVoiceWakeup(isChecked, (code) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                            switchVadWakeup.setChecked(!isChecked);
                        }
                    });
                });
            }
        });
        
        // 游戏模式开关
        switchGameMode = findViewById(R.id.switch_game_mode);
        switchGameMode.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "游戏模式开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置游戏模式
                commandApi.setGameMode(isChecked, (code) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                            switchGameMode.setChecked(!isChecked);
                        }
                    });
                });
            }
        });
        
        // 掉落提醒开关
        switchFallAlert = findViewById(R.id.switch_fall_alert);
        switchFallAlert.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "掉落提醒开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置掉落提醒
                commandApi.setFallAlert(isChecked, (code) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                            switchFallAlert.setChecked(!isChecked);
                        }
                    });
                });
            }
        });
        
        // 寻找耳机开关
        switchEarLocation = findViewById(R.id.switch_ear_location);
        switchEarLocation.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "寻找耳机开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置寻找耳机功能
                commandApi.setEarLocation(isChecked, (code) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                            switchEarLocation.setChecked(!isChecked);
                        }
                    });
                });
            }
        });
        
        // 寻找提示音开关
        switchFindAlert = findViewById(R.id.switch_find_alert);
        switchFindAlert.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(HMSwitch buttonView, boolean isChecked) {
                Log.d(TAG, "寻找提示音开关状态变更: " + isChecked);
                
                // 使用YoCommandApi设置寻找提示音功能
                commandApi.setFindAlert(isChecked, (code) -> {
                    runOnUiThread(() -> {
                        // 显示结果消息
                        String message = (code == YoCommandApi.CommandResultCode.SUCCESS) ? 
                                "设置成功" : "设置失败";
                        Toast.makeText(FunctionSettingsActivity.this, message, Toast.LENGTH_SHORT).show();
                        
                        // 如果失败，恢复开关状态
                        if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                            switchFindAlert.setChecked(!isChecked);
                        }
                    });
                });
            }
        });
    }
} 