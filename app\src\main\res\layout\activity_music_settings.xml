<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/music_settings_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.MusicSettingsActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/comp_background_gray"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="音乐设置"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 使用ScrollView确保所有内容可滚动 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 音乐设置标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="音乐设置"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="16dp"/>

            <!-- 高音质模式（GGEC UA）-->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="高音质模式（GGEC UA）"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"
                        android:layout_marginBottom="12dp"/>

                    <RadioGroup
                        android:id="@+id/rg_sound_quality_mode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        
                        <RadioButton
                            android:id="@+id/rb_high_quality"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="高音质模式"
                            android:checked="true"
                            android:layout_marginBottom="8dp"/>
                            
                        <RadioButton
                            android:id="@+id/rb_high_endurance"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="高续航模式"/>
                    </RadioGroup>
                    
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="设置: 高音质模式/高续航模式 (默认: 高音质)"
                        android:textSize="12sp"
                        android:textColor="@color/font_secondary"
                        android:layout_marginTop="12dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 音量自适应 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="音量自适应"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"/>

                    <com.ggec.hs01.view.HMSwitch
                        android:id="@+id/switch_volume_adaptive"
                        style="@style/StandardSwitch" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
            
            <!-- EQ管理 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="EQ管理"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"
                        android:layout_marginBottom="12dp"/>

                    <RadioGroup
                        android:id="@+id/rg_eq_mode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        
                        <RadioButton
                            android:id="@+id/rb_movie_mode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="电影模式"
                            android:layout_marginBottom="8dp"/>
                            
                        <RadioButton
                            android:id="@+id/rb_news_mode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="新闻模式"
                            android:layout_marginBottom="8dp"/>
                            
                        <RadioButton
                            android:id="@+id/rb_music_mode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="音乐模式"
                            android:checked="true"
                            android:layout_marginBottom="8dp"/>
                            
                        <RadioButton
                            android:id="@+id/rb_human_voice_mode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="老年人模式"/>
                    </RadioGroup>
                    
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="默认: 音乐模式"
                        android:textSize="12sp"
                        android:textColor="@color/font_secondary"
                        android:layout_marginTop="12dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
            
            <!-- 听力保护 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="听力保护"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"
                        android:layout_marginBottom="12dp"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="音量提醒 开/关"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <com.ggec.hs01.view.HMSwitch
                            android:id="@+id/switch_volume_reminder"
                            style="@style/StandardSwitch" />
                    </LinearLayout>
                    

                        
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="默认: 关"
                        android:textSize="12sp"
                        android:textColor="@color/font_secondary"
                        android:layout_marginTop="12dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout> 