<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/app_settings_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/comp_background_gray"
    android:fitsSystemWindows="false"
    tools:context=".activity.AppSettingsActivity">

    <!-- 顶部导航栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/comp_background_gray"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="4dp"
            android:padding="12dp"
            android:src="@drawable/ic_public_back"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="应用设置"
            android:textColor="@color/font_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 使用ScrollView确保所有内容可滚动 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintTop_toBottomOf="@+id/nav_bar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 应用设置标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="应用设置"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/font_primary"
                android:layout_marginBottom="16dp"/>

            <!-- 产品信息 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="产品信息"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"
                        android:layout_marginBottom="12dp"/>

                    <!-- 获取产品信息按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp"
                        android:gravity="center">

                        <com.ggec.hs01.view.HMButton
                            android:id="@+id/btn_get_product_info"
                            android:layout_width="wrap_content"
                            android:layout_height="36dp"
                            android:text="获取产品信息"
                            android:textSize="15sp" />

                        <ProgressBar
                            android:id="@+id/pb_loading"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginStart="12dp"
                            android:visibility="gone" />
                    </LinearLayout>

                    <!-- 设备名称 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="设备名称"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <TextView
                            android:id="@+id/tv_device_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"/>
                    </LinearLayout>

                    <!-- 产品型号 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="产品型号"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <TextView
                            android:id="@+id/tv_product_model"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"/>
                    </LinearLayout>

                    <!-- 序列号 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="序列号"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <TextView
                            android:id="@+id/tv_serial_number"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"/>
                    </LinearLayout>

                    <!-- 经典蓝牙MAC -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="MAC地址"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <TextView
                            android:id="@+id/tv_classic_bt_mac"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"/>
                    </LinearLayout>

                    <!-- 耳机颜色 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="耳机颜色"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <TextView
                            android:id="@+id/tv_ear_color"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"/>
                    </LinearLayout>

                    <!-- Feature Code -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Feature Code"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <TextView
                            android:id="@+id/tv_feature_code"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textSize="14sp"
                            android:textColor="@color/font_secondary"/>
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 设备管理 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="设备管理"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"
                        android:layout_marginBottom="12dp"/>

                    <!-- 清除配对记录 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="清除配对记录"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <com.ggec.hs01.view.HMButton
                            android:id="@+id/btn_clear_pairing"
                            android:layout_width="75dp"
                            android:layout_height="32dp"
                            android:text="清除"
                            android:textSize="15sp" />
                    </LinearLayout>

                    <!-- 修改设备名称 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="修改设备名称"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <com.ggec.hs01.view.HMButton
                            android:id="@+id/btn_edit_device_name"
                            android:layout_width="75dp"
                            android:layout_height="32dp"
                            android:text="修改"
                            android:textSize="15sp" />
                    </LinearLayout>
                    
                    <!-- 获取经典蓝牙MAC地址 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">
                            
                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="经典蓝牙MAC地址"
                                android:textSize="14sp"
                                android:textColor="@color/font_primary"/>
                            
                            <TextView
                                android:id="@+id/tv_classic_bt_mac"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="--"
                                android:textSize="12sp"
                                android:textColor="@color/font_secondary"/>
                        </LinearLayout>

                        <com.ggec.hs01.view.HMButton
                            android:id="@+id/btn_get_classic_bt_mac"
                            android:layout_width="75dp"
                            android:layout_height="32dp"
                            android:text="获取"
                            android:textSize="15sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 固件升级 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="固件升级"
                        android:textSize="16sp"
                        android:textColor="@color/font_primary"
                        android:layout_marginBottom="12dp"/>

                    <!-- OTA升级 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="OTA升级"
                            android:textSize="14sp"
                            android:textColor="@color/font_primary"/>

                        <com.ggec.hs01.view.HMButton
                            android:id="@+id/btn_ota_update"
                            android:layout_width="75dp"
                            android:layout_height="32dp"
                            android:text="进入"
                            android:textSize="15sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>

    <ProgressBar
        android:id="@+id/pb_loading"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible"/>

</androidx.constraintlayout.widget.ConstraintLayout> 