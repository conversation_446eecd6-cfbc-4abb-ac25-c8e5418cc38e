package com.ggec.hs01.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.InputFilter;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.ggec.bleservice.YoBLEApi;
import com.ggec.bleservice.YoCommandApi;
import com.ggec.hs01.R;
import com.ggec.hs01.view.HMButton;
import java.util.Map;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * 应用设置页面
 * 负责显示和处理应用设置相关的功能
 */
public class AppSettingsActivity extends AppCompatActivity {
    
    // UI组件
    private TextView tvDeviceName;
    private TextView tvProductModel;
    private TextView tvSerialNumber;
    private TextView tvEarColor;
    private TextView tvClassicBtMac;
    private TextView tvFeatureCode;
    private HMButton btnGetProductInfo;
    private HMButton btnClearPairing;
    private HMButton btnEditDeviceName;
    private HMButton btnGetClassicBtMac;
    private HMButton btnOtaUpdate;
    private ProgressBar pbLoading;
    
    // API实例
    private YoCommandApi commandApi;
    private YoBLEApi bleApi;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_app_settings);
        
        // 获取API实例
        commandApi = YoCommandApi.getInstance();
        bleApi = YoBLEApi.getInstance();
        
        // 初始化视图
        initViews();
        
        // 初始化返回按钮
        initBackButton();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.app_settings_main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        
        // 设置点击事件
        setupClickListeners();
    }
    
    /**
     * 初始化视图组件
     */
    private void initViews() {
        tvDeviceName = findViewById(R.id.tv_device_name);
        tvProductModel = findViewById(R.id.tv_product_model);
        tvSerialNumber = findViewById(R.id.tv_serial_number);
        tvEarColor = findViewById(R.id.tv_ear_color);
        tvClassicBtMac = findViewById(R.id.tv_classic_bt_mac);
        tvFeatureCode = findViewById(R.id.tv_feature_code);
        btnGetProductInfo = findViewById(R.id.btn_get_product_info);
        btnClearPairing = findViewById(R.id.btn_clear_pairing);
        btnEditDeviceName = findViewById(R.id.btn_edit_device_name);
        btnGetClassicBtMac = findViewById(R.id.btn_get_classic_bt_mac);
        btnOtaUpdate = findViewById(R.id.btn_ota_update);
        pbLoading = findViewById(R.id.pb_loading);
    }
    
    /**
     * 初始化返回按钮
     */
    private void initBackButton() {
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
    }
    
    /**
     * 设置点击事件监听
     */
    private void setupClickListeners() {
        // 获取产品信息按钮
        btnGetProductInfo.setOnClickListener(v -> getProductInfo());
        
        // 清除配对记录按钮
        btnClearPairing.setOnClickListener(v -> showClearPairingConfirmDialog());
        
        // 修改设备名称按钮
        btnEditDeviceName.setOnClickListener(v -> showEditDeviceNameDialog());
        
        // 获取经典蓝牙MAC地址按钮
        btnGetClassicBtMac.setOnClickListener(v -> getClassicBtMacAddress());
        
        // OTA升级按钮
        btnOtaUpdate.setOnClickListener(v -> navigateToOtaUpdate());
    }
    
    /**
     * 获取产品信息（耳机颜色、固件版本、设备名称）
     */
    private void getProductInfo() {
        // 检查设备是否已连接
        if (bleApi.getConnectedDevice() == null) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 显示加载中
        pbLoading.setVisibility(View.VISIBLE);
        btnGetProductInfo.setEnabled(false);
        
        commandApi.getAboutDeviceStatus(new YoCommandApi.AboutDeviceStatusCallback() {
            @Override
            public void onAboutDeviceStatusResult(int code, Map<String, String> statusMap) {
                runOnUiThread(() -> {
                    // 打印原始数据
                    for (Map.Entry<String, String> entry : statusMap.entrySet()) {
                        android.util.Log.i("AppSettings", entry.getKey() + " = " + entry.getValue());
                    }

                    // 更新UI
                    tvDeviceName.setText(statusMap.getOrDefault("70", "获取失败"));
                    tvProductModel.setText(statusMap.getOrDefault("71", "获取失败"));
                    tvSerialNumber.setText(statusMap.getOrDefault("72", "获取失败"));
                    tvClassicBtMac.setText(statusMap.getOrDefault("73", "获取失败"));
                    tvEarColor.setText(statusMap.getOrDefault("74", "获取失败"));
                    tvFeatureCode.setText(statusMap.getOrDefault("75", "获取失败"));

                    // 隐藏加载中
                    pbLoading.setVisibility(View.GONE);
                    btnGetProductInfo.setEnabled(true);

                    if (code != YoCommandApi.CommandResultCode.SUCCESS) {
                        Toast.makeText(AppSettingsActivity.this, "部分信息获取失败", Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }
    
    /**
     * 显示清除配对记录确认对话框
     */
    private void showClearPairingConfirmDialog() {
        // 检查设备是否已连接
        if (bleApi.getConnectedDevice() == null) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            return;
        }
        
        new AlertDialog.Builder(this)
            .setTitle("清除配对记录")
            .setMessage("确定要清除耳机上的配对记录吗？清除后需要重新配对连接。")
            .setPositiveButton("确定", (dialog, which) -> clearPairing())
            .setNegativeButton("取消", null)
            .show();
    }
    
    /**
     * 清除配对记录
     */
    private void clearPairing() {
        // 显示加载中
        btnClearPairing.setEnabled(false);
        
        commandApi.clearPairing(new YoCommandApi.CommandResultCallback() {
            @Override
            public void onCommandResult(int code) {
                runOnUiThread(() -> {
                    btnClearPairing.setEnabled(true);
                    
                    if (code == YoCommandApi.CommandResultCode.SUCCESS) {
                        Toast.makeText(AppSettingsActivity.this, "清除配对记录成功", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(AppSettingsActivity.this, "清除配对记录失败", Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }
    
    /**
     * 显示修改设备名称对话框
     */
    private void showEditDeviceNameDialog() {
        // 检查设备是否已连接
        if (bleApi.getConnectedDevice() == null) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 创建输入框
        EditText editText = new EditText(this);
        editText.setText(tvDeviceName.getText());
        
        // 设置最大输入长度为70个字符（约210字节的UTF-8编码）
        editText.setFilters(new InputFilter[] {new InputFilter.LengthFilter(70)});
        
        new AlertDialog.Builder(this)
            .setTitle("修改设备名称")
            .setMessage("请输入新的设备名称")
            .setView(editText)
            .setPositiveButton("确定", (dialog, which) -> {
                String newName = editText.getText().toString().trim();
                if (newName.isEmpty()) {
                    Toast.makeText(AppSettingsActivity.this, "设备名称不能为空", Toast.LENGTH_SHORT).show();
                } else {
                    setDeviceName(newName);
                }
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    /**
     * 修改设备名称
     * @param newName 新的设备名称
     */
    private void setDeviceName(String newName) {
        // 显示加载中
        pbLoading.setVisibility(View.VISIBLE);
        btnEditDeviceName.setEnabled(false);
        
        commandApi.setDeviceName(newName, new YoCommandApi.CommandResultCallback() {
            @Override
            public void onCommandResult(int code) {
                runOnUiThread(() -> {
                    // 隐藏加载中
                    pbLoading.setVisibility(View.GONE);
                    btnEditDeviceName.setEnabled(true);
                    
                    if (code == YoCommandApi.CommandResultCode.SUCCESS) {
                        // 更新UI上的设备名称
                        tvDeviceName.setText(newName);
                        Toast.makeText(AppSettingsActivity.this, "修改设备名称成功", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(AppSettingsActivity.this, "修改设备名称失败", Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }
    
    /**
     * 获取经典蓝牙MAC地址
     */
    private void getClassicBtMacAddress() {
        // 检查设备是否已连接
        if (bleApi.getConnectedDevice() == null) {
            Toast.makeText(this, "请先连接设备", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 显示加载中
        pbLoading.setVisibility(View.VISIBLE);
        btnGetClassicBtMac.setEnabled(false);
        
        commandApi.getDeviceMacAddress(new YoCommandApi.ClassicBtAddressCallback() {
            @Override
            public void onClassicBtAddressResult(int code, String macAddress) {
                runOnUiThread(() -> {
                    // 隐藏加载中
                    pbLoading.setVisibility(View.GONE);
                    btnGetClassicBtMac.setEnabled(true);
                    
                    if (code == YoCommandApi.CommandResultCode.SUCCESS) {
                        // 更新UI上的MAC地址
                        tvClassicBtMac.setText(macAddress);
                        Toast.makeText(AppSettingsActivity.this, "获取经典蓝牙MAC地址成功", Toast.LENGTH_SHORT).show();
                    } else {
                        tvClassicBtMac.setText("获取失败");
                        Toast.makeText(AppSettingsActivity.this, "获取经典蓝牙MAC地址失败", Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 这里不要释放YoCommandApi，因为它可能在其他地方还在使用
    }
    
    /**
     * 跳转到OTA升级页面
     */
    private void navigateToOtaUpdate() {
        Intent intent = new Intent(AppSettingsActivity.this, OtaUpdateActivity.class);
        startActivity(intent);
    }
} 