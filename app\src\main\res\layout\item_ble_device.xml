<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:padding="16dp">

    <ImageView
        android:id="@+id/iv_ble_bluetooth_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/ic_public_bluetooth"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_ble_device_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/font_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_ble_bluetooth_icon"
        app:layout_constraintTop_toTopOf="@+id/iv_ble_bluetooth_icon"
        tools:text="蓝牙设备名称" />

    <TextView
        android:id="@+id/tv_ble_device_address"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/font_secondary"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_ble_bluetooth_icon"
        app:layout_constraintEnd_toStartOf="@+id/tv_ble_device_rssi"
        app:layout_constraintStart_toEndOf="@+id/iv_ble_bluetooth_icon"
        app:layout_constraintTop_toBottomOf="@+id/tv_ble_device_name"
        tools:text="00:11:22:33:44:55" />

    <TextView
        android:id="@+id/tv_ble_device_rssi"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/font_secondary"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_ble_device_address"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_ble_device_address"
        tools:text="-75 dBm" />

</androidx.constraintlayout.widget.ConstraintLayout> 