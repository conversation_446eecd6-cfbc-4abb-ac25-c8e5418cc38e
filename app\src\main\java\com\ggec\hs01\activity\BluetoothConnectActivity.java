package com.ggec.hs01.activity;

import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.pm.PackageManager;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ggec.hs01.R;
import com.ggec.bleservice.YoBLEApi;
import com.ggec.hs01.utils.BluetoothDeviceAdapter;
import com.ggec.hs01.GGECHSApplication;

/**
 * Author: tingfeng.yang
 * Date: 2025-07-08
 * Description: 
 * 蓝牙连接页面
 * 实现蓝牙设备扫描和连接
 */
public class BluetoothConnectActivity extends AppCompatActivity {
    private static final String TAG = "BluetoothConnectActivity";
    private static final int SCAN_TIMEOUT = 15000; // 扫描超时时间（毫秒）
    
    // 权限请求码
    private static final int REQUEST_BLUETOOTH_PERMISSIONS = 1001;
    
    // UI组件
    private TextView tvScanStatus;
    private TextView tvNoDevices;
    private ProgressBar progressScanning;
    private RecyclerView rvBluetoothDevices;
    
    // 蓝牙相关
    private BluetoothDeviceAdapter deviceAdapter;
    private boolean isConnecting = false;
    private BluetoothDevice connectedDevice = null;
    
    // YoBLEApi 实例
    private YoBLEApi bleApi;
    
    // 蓝牙事件监听器
    private final YoBLEApi.BleListener bleListener = new YoBLEApi.BleListener() {
        @Override
        public void onConnectionStateChanged(BluetoothDevice device, boolean connected) {
            runOnUiThread(() -> {
                isConnecting = false;
                
                if (connected) {
                    connectedDevice = device;
                    updateScanStatus("已连接");
                    Toast.makeText(BluetoothConnectActivity.this, "连接成功，正在发现服务", Toast.LENGTH_SHORT).show();
                    
                    new android.os.Handler().postDelayed(
                        BluetoothConnectActivity.this::handleConnectionSuccess, 1000);
                } else {
                    connectedDevice = null;
                    updateScanStatus("已断开连接");
                    progressScanning.setVisibility(View.GONE);
                }
            });
        }
        
        @Override
        public void onDeviceFound(BluetoothDevice device, int rssi) {
            runOnUiThread(() -> {
                deviceAdapter.addDevice(device, rssi);
                
                if (tvNoDevices.getVisibility() == View.VISIBLE) {
                    tvNoDevices.setVisibility(View.GONE);
                }
            });
        }
        
        @Override
        public void onScanStarted() {
            runOnUiThread(() -> {
                updateScanStatus("扫描开始");
                progressScanning.setVisibility(View.VISIBLE);
            });
        }
        
        @Override
        public void onScanFinished() {
            runOnUiThread(() -> {
                updateScanStatus("扫描完成");
                progressScanning.setVisibility(View.GONE);
                
                if (deviceAdapter.getItemCount() == 0) {
                    tvNoDevices.setVisibility(View.VISIBLE);
                    tvNoDevices.setText("未找到蓝牙设备");
                }
            });
        }
        
        @Override
        public void onError(String errorMessage) {
            runOnUiThread(() -> {
                isConnecting = false;
                updateScanStatus("连接失败");
                progressScanning.setVisibility(View.GONE);
                Toast.makeText(BluetoothConnectActivity.this, "连接失败，请重试", Toast.LENGTH_SHORT).show();
            });
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_bluetooth_connect);
        
        // 获取YoBLEApi实例
        bleApi = ((GGECHSApplication) getApplication()).getBleApi();
        
        // 初始化UI组件
        initViews();
        
        // 设置内容区域适应系统UI，保留内边距防止内容被状态栏遮挡
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.bluetooth_connect_main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        
        // 初始化蓝牙设备适配器
        deviceAdapter = new BluetoothDeviceAdapter();
        deviceAdapter.setOnDeviceClickListener(this::onDeviceSelected);
        rvBluetoothDevices.setAdapter(deviceAdapter);
        
        // 检查蓝牙权限
        checkBluetoothPermissions();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 注册回调
        bleApi.registerListener(bleListener);
        
        // 如果已经获得权限，自动开始扫描
        if (bleApi.checkBluetoothPermissions(this)) {
            startScan();
        }
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 停止扫描
        stopScan();
        
        // 注销回调
        bleApi.unregisterListener(bleListener);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 不在此断开蓝牙连接，只有在应用退出时才断开
    }
    
    /**
     * 初始化UI视图
     */
    private void initViews() {
        tvScanStatus = findViewById(R.id.tv_scan_status);
        tvNoDevices = findViewById(R.id.tv_no_devices);
        progressScanning = findViewById(R.id.progress_scanning);
        rvBluetoothDevices = findViewById(R.id.rv_bluetooth_devices);
        
        // 设置返回按钮点击事件
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());
        
        // 设置刷新按钮点击事件
        ImageView btnRefresh = findViewById(R.id.btn_refresh);
        btnRefresh.setOnClickListener(v -> {
            stopScan();
            startScan();
        });
        
        // 设置RecyclerView
        rvBluetoothDevices.setLayoutManager(new LinearLayoutManager(this));
        
        // 启用蓝牙设备名称过滤
        bleApi.setFilterNoNameDevices(true);
    }
    
    /**
     * 更新扫描状态显示
     */
    private void updateScanStatus(String status) {
        if (tvScanStatus != null) {
            tvScanStatus.setText(status);
        }
    }
    
    /**
     * 检查蓝牙权限
     */
    private void checkBluetoothPermissions() {
        if (!bleApi.checkBluetoothPermissions(this)) {
            updateScanStatus("等待授予蓝牙权限");
            progressScanning.setVisibility(View.GONE);
        }
    }
    
    /**
     * 开始蓝牙扫描
     */
    private void startScan() {
        // 如果蓝牙不可用，显示提示
        if (!bleApi.isBluetoothSupported()) {
            updateScanStatus("此设备不支持蓝牙");
            tvNoDevices.setVisibility(View.VISIBLE);
            tvNoDevices.setText("此设备不支持蓝牙");
            progressScanning.setVisibility(View.GONE);
            return;
        }
        
        // 如果蓝牙未开启，显示提示
        if (!bleApi.isBluetoothEnabled()) {
            updateScanStatus("蓝牙未开启");
            tvNoDevices.setVisibility(View.VISIBLE);
            tvNoDevices.setText("请开启蓝牙后重试");
            progressScanning.setVisibility(View.GONE);
            return;
        }
        
        // 清空之前的设备列表
        deviceAdapter.clearDevices();
        
        // 更新UI状态
        updateScanStatus("正在扫描...");
        progressScanning.setVisibility(View.VISIBLE);
        tvNoDevices.setVisibility(View.GONE);
        
        // 开始扫描
        if (!bleApi.startScan(SCAN_TIMEOUT)) {
            updateScanStatus("无法启动扫描");
            progressScanning.setVisibility(View.GONE);
        }
    }
    
    /**
     * 停止蓝牙扫描
     */
    private void stopScan() {
        bleApi.stopScan();
        progressScanning.setVisibility(View.GONE);
    }
    
    /**
     * 处理设备选择
     */
    private void onDeviceSelected(BluetoothDevice device) {
        if (isConnecting) {
            return;
        }
        
        // 停止扫描
        stopScan();
        
        isConnecting = true;
        updateScanStatus("正在连接...");
        bleApi.connect(device);
    }
    
    /**
     * 处理连接成功事件
     */
    private void handleConnectionSuccess() {
        setResult(Activity.RESULT_OK);
        finish();
    }
    
    //--- 权限请求结果处理 ---//
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        // 使用常量替代硬编码值
        if (requestCode == REQUEST_BLUETOOTH_PERMISSIONS) {
            boolean allGranted = true;
            
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            
            if (allGranted) {
                // 权限已授予，开始扫描
                startScan();
            } else {
                // 权限被拒绝
                updateScanStatus("蓝牙权限被拒绝");
                tvNoDevices.setVisibility(View.VISIBLE);
                tvNoDevices.setText("需要蓝牙权限才能使用此功能");
                progressScanning.setVisibility(View.GONE);
            }
        }
    }
} 