<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AppInsightsSettings">
    <option name="selectedTabId" value="Firebase Crashlytics" />
    <option name="tabSettings">
      <map>
        <entry key="Firebase Crashlytics">
          <value>
            <InsightsFilterSettings>
              <option name="connection">
                <ConnectionSetting>
                  <option name="appId" value="com.ggec.hs01" />
                  <option name="mobileSdkAppId" value="1:629405857355:android:49495b850f573fa758ffce" />
                  <option name="projectId" value="ggec-hs01" />
                  <option name="projectNumber" value="629405857355" />
                </ConnectionSetting>
              </option>
              <option name="signal" value="SIGNAL_UNSPECIFIED" />
              <option name="timeIntervalDays" value="THIRTY_DAYS" />
              <option name="visibilityType" value="ALL" />
            </InsightsFilterSettings>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>